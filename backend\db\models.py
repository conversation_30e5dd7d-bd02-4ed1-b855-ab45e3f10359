"""SQLAlchemy models for TrustChain-Auth."""

from sqlalchemy import (
    Column, Integer, String, Float, DateTime,
    Boolean, ForeignKey, JSON, Enum as SQLAEnum
)
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from db.session import Base

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"
    DEVICE = "device"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    first_name = Column(String)
    last_name = Column(String)
    full_name = Column(String)
    role = Column(SQLAEnum(UserRole), default=UserRole.USER)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    devices = relationship("Device", back_populates="user")
    auth_events = relationship("AuthEvent", back_populates="user")
    risk_scores = relationship("RiskScore", back_populates="user")

class Device(Base):
    __tablename__ = "devices"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    device_id = Column(String, unique=True, index=True, nullable=False)
    device_type = Column(String)
    device_info = Column(JSON)
    is_trusted = Column(Boolean, default=False)
    last_used = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="devices")
    auth_events = relationship("AuthEvent", back_populates="device")

class AuthEvent(Base):
    __tablename__ = "auth_events"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    device_id = Column(Integer, ForeignKey("devices.id"))
    event_type = Column(String, nullable=False)
    risk_score = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)
    location = Column(JSON)
    success = Column(Boolean, default=False)
    
    # Relationships
    user = relationship("User", back_populates="auth_events")
    device = relationship("Device", back_populates="auth_events")

class RiskScore(Base):
    __tablename__ = "risk_scores"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    score = Column(Float, nullable=False)
    factors = Column(JSON)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="risk_scores")

class SecurityAlert(Base):
    __tablename__ = "security_alerts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    alert_type = Column(String, nullable=False)
    severity = Column(String, nullable=False)
    description = Column(String)
    timestamp = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)
    is_resolved = Column(Boolean, default=False)
    resolution_notes = Column(String)
    resolved_at = Column(DateTime)
