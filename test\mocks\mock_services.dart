import 'package:mockito/mockito.dart';
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart';
import 'package:trustchain_auth/features/security/domain/services/security_policy_service.dart';
import 'package:trustchain_auth/features/banking/domain/models/models.dart';
import 'package:trustchain_auth/core/enums.dart';

// Mock classes for testing
class MockContinuousAuthService extends Mock implements ContinuousAuthService {}

class MockSecurityPolicyService extends Mock implements SecurityPolicyService {}

// Mock BLoC for testing
class MockBankingBloc extends Mock {
  Stream<BankingState> get stream => Stream.value(
    BankingState.loaded(
      accounts: [],
      transactions: [],
      isLoading: false,
      error: null,
      exportSuccess: false,
    ),
  );
}
