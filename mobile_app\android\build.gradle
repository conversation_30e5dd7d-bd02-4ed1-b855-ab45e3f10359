buildscript {
    ext.kotlin_version = '1.9.25'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://storage.googleapis.com/download.flutter.io'
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                compileSdkVersion 34

                // Add Java 17 compatibility for all modules
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }
                
                // Configure Kotlin JVM target
                if (project.hasProperty("kotlin")) {
                    kotlinOptions {
                        jvmTarget = '17'
                    }
                }

                if (project.hasProperty("buildTypes")) {
                    buildTypes {
                        release {
                            minifyEnabled false
                            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
                        }
                    }
                }
            }
        }

        // Configure JVM toolchain for all projects
        if (project.hasProperty("kotlin")) {
            project.kotlin {
                jvmToolchain(17)
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
