"""Database models for behavioral data."""

from sqlalchemy import <PERSON>umn, Integer, String, Float, JSON, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_class import Base

class BehavioralEvent(Base):
    """Behavioral events like typing and touch interactions."""
    __tablename__ = "behavioral_events"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    event_type = Column(String)  # 'typing', 'touch', etc.
    data = Column(JSON)  # Event-specific data
    context = Column(JSON)  # Device context, app state, etc.
    
    # Relationships
    user = relationship("User", back_populates="behavioral_events")
    device = relationship("Device", back_populates="behavioral_events")

class BehavioralFeature(Base):
    """Extracted behavioral features."""
    __tablename__ = "behavioral_features"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    feature_type = Column(String)  # 'typing', 'touch', 'combined'
    features = Column(JSON)  # Extracted feature vector
    feature_metadata = Column(JSON)  # Feature extraction metadata
    
    # Relationships
    user = relationship("User", back_populates="behavioral_features")
    device = relationship("Device", back_populates="behavioral_features")

class BehavioralProfile(Base):
    """User's behavioral profile."""
    __tablename__ = "behavioral_profiles"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Profile data
    profile_type = Column(String)  # 'baseline', 'current', etc.
    latent_features = Column(JSON)  # Encoded behavioral features
    metrics = Column(JSON)  # Profile quality metrics
    thresholds = Column(JSON)  # Anomaly detection thresholds
    profile_metadata = Column(JSON)  # Training metadata, version info, etc.
    
    # Relationships
    user = relationship("User", back_populates="behavioral_profile")

class RiskAssessment(Base):
    """Behavioral risk assessments."""
    __tablename__ = "risk_assessments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_id = Column(Integer, ForeignKey("devices.id"), index=True)
    session_id = Column(String, index=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Risk scores
    risk_score = Column(Float)
    anomaly_score = Column(Float)
    confidence = Column(Float)
    
    # Assessment details
    features_used = Column(JSON)  # List of features used
    model_version = Column(String)  # Model version used
    decision = Column(String)  # 'allow', 'block', 'challenge'
    explanation = Column(JSON)  # Decision explanation
    assessment_metadata = Column(JSON)  # Additional assessment metadata
    
    # Relationships
    user = relationship("User", back_populates="risk_assessments")
    device = relationship("Device", back_populates="risk_assessments")
