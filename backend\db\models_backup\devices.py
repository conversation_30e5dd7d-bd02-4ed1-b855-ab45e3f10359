"""Device model."""

from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON>, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from db.base_class import Base

class Device(Base):
    """Device model for managing user devices."""
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    device_type = Column(String)  # mobile, desktop, tablet, etc.
    device_id = Column(String, unique=True, index=True)  # Unique device identifier
    device_name = Column(String)  # User-friendly device name
    platform = Column(String)  # iOS, Android, Windows, etc.
    last_used = Column(DateTime, default=datetime.utcnow)
    is_trusted = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    device_info = Column(JSON)  # Additional device information    # Relationships
    user = relationship("User", back_populates="devices")
    auth_events = relationship("AuthEvent", back_populates="device")
    security_alerts = relationship("SecurityAlert", back_populates="device")
    behavioral_events = relationship("BehavioralEvent", back_populates="device")
    behavioral_features = relationship("BehavioralFeature", back_populates="device")
    risk_assessments = relationship("RiskAssessment", back_populates="device")
