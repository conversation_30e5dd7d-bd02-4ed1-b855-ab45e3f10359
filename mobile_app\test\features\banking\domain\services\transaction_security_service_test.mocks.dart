// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in trustchain_auth/test/features/banking/domain/services/transaction_security_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:trustchain_auth/core/enums.dart' as _i6;
import 'package:trustchain_auth/features/auth/domain/models/auth_models.dart'
    as _i3;
import 'package:trustchain_auth/features/auth/domain/models/security_score.dart'
    as _i2;
import 'package:trustchain_auth/features/auth/domain/services/continuous_auth_service.dart'
    as _i4;
import 'package:trustchain_auth/features/auth/domain/services/security_policy_service.dart'
    as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSecurityScore_0 extends _i1.SmartFake implements _i2.SecurityScore {
  _FakeSecurityScore_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSecurityRequirement_1 extends _i1.SmartFake
    implements _i3.SecurityRequirement {
  _FakeSecurityRequirement_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ContinuousAuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockContinuousAuthService extends _i1.Mock
    implements _i4.ContinuousAuthService {
  MockContinuousAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i3.DomainAuthEvent> get authEvents => (super.noSuchMethod(
        Invocation.getter(#authEvents),
        returnValue: _i5.Stream<_i3.DomainAuthEvent>.empty(),
      ) as _i5.Stream<_i3.DomainAuthEvent>);

  @override
  _i5.Stream<_i2.SecurityScore> get securityScoreStream => (super.noSuchMethod(
        Invocation.getter(#securityScoreStream),
        returnValue: _i5.Stream<_i2.SecurityScore>.empty(),
      ) as _i5.Stream<_i2.SecurityScore>);

  @override
  _i5.Future<double> getCurrentConfidenceLevel() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentConfidenceLevel,
          [],
        ),
        returnValue: _i5.Future<double>.value(0.0),
      ) as _i5.Future<double>);

  @override
  _i5.Future<_i2.SecurityScore> getCurrentSecurityScore() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentSecurityScore,
          [],
        ),
        returnValue: _i5.Future<_i2.SecurityScore>.value(_FakeSecurityScore_0(
          this,
          Invocation.method(
            #getCurrentSecurityScore,
            [],
          ),
        )),
      ) as _i5.Future<_i2.SecurityScore>);

  @override
  _i5.Future<void> startMonitoring() => (super.noSuchMethod(
        Invocation.method(
          #startMonitoring,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void stopMonitoring() => super.noSuchMethod(
        Invocation.method(
          #stopMonitoring,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void recordActivity() => super.noSuchMethod(
        Invocation.method(
          #recordActivity,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  double calculateRiskScore({
    required double? trustScore,
    required List<_i3.DomainAuthEvent>? recentEvents,
    required String? currentLocation,
    required bool? isKnownDevice,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #calculateRiskScore,
          [],
          {
            #trustScore: trustScore,
            #recentEvents: recentEvents,
            #currentLocation: currentLocation,
            #isKnownDevice: isKnownDevice,
          },
        ),
        returnValue: 0.0,
      ) as double);

  @override
  _i6.RiskLevel getRiskLevel(double? riskScore) => (super.noSuchMethod(
        Invocation.method(
          #getRiskLevel,
          [riskScore],
        ),
        returnValue: _i6.RiskLevel.low,
      ) as _i6.RiskLevel);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [SecurityPolicyService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSecurityPolicyService extends _i1.Mock
    implements _i7.SecurityPolicyService {
  MockSecurityPolicyService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Stream<_i3.SecurityPolicyEvent> get policyEvents => (super.noSuchMethod(
        Invocation.getter(#policyEvents),
        returnValue: _i5.Stream<_i3.SecurityPolicyEvent>.empty(),
      ) as _i5.Stream<_i3.SecurityPolicyEvent>);

  @override
  bool get requireChallengeForMediumRisk => (super.noSuchMethod(
        Invocation.getter(#requireChallengeForMediumRisk),
        returnValue: false,
      ) as bool);

  @override
  bool get isBiometricVerificationEnabled => (super.noSuchMethod(
        Invocation.getter(#isBiometricVerificationEnabled),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<void> startEnforcement() => (super.noSuchMethod(
        Invocation.method(
          #startEnforcement,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void stopEnforcement() => super.noSuchMethod(
        Invocation.method(
          #stopEnforcement,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void recordFailedAttempt() => super.noSuchMethod(
        Invocation.method(
          #recordFailedAttempt,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void resetFailedAttempts() => super.noSuchMethod(
        Invocation.method(
          #resetFailedAttempts,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool isAccountLocked() => (super.noSuchMethod(
        Invocation.method(
          #isAccountLocked,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<_i3.SecurityRequirement> evaluateActionSecurity(
          _i3.SecurityAction? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #evaluateActionSecurity,
          [action],
        ),
        returnValue: _i5.Future<_i3.SecurityRequirement>.value(
            _FakeSecurityRequirement_1(
          this,
          Invocation.method(
            #evaluateActionSecurity,
            [action],
          ),
        )),
      ) as _i5.Future<_i3.SecurityRequirement>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
