# TrustChain-Auth Project Restructuring Plan

## 🎯 Objectives
- Create a clean, logical directory structure
- Eliminate redundancies and confusion
- Follow industry best practices
- Improve developer experience
- Maintain all functionality

## 📊 Current Issues Identified

### 1. **Duplicate Directories**
- `lib/` and `mobile_app/lib/` (Flutter code scattered)
- `test/` and `tests/` and `mobile_app/test/` (tests scattered)
- `integration_test/` and `mobile_app/integration_test/`

### 2. **Configuration Chaos**
- `.env.example` at root
- `config/development/.env.example`
- Settings scattered in multiple files
- Docker configs mixed with app configs

### 3. **Dependency Management Issues**
- `requirements.txt` at root (dev tools)
- `backend/requirements.txt` (backend deps)
- `ml_models/requirements.txt` (ML deps)
- `pyproject.toml` (duplicates backend deps)
- `setup.py` (duplicates everything)

### 4. **Backend Model Organization**
- Models split across multiple files unnecessarily
- Foreign key type mismatches
- Inconsistent import patterns

## 🏗️ Proposed New Structure

```
TrustChain-Auth/
├── README.md
├── LICENSE
├── docker-compose.yml
├── .gitignore
├── .env.example
│
├── apps/                          # Application components
│   ├── mobile/                    # Flutter mobile app (consolidated)
│   ├── backend/                   # FastAPI backend (reorganized)
│   └── ml-service/                # ML inference service (renamed from ml_models)
│
├── config/                        # All configuration files
│   ├── development/
│   ├── production/
│   ├── testing/
│   └── docker/
│
├── docs/                          # Documentation (consolidated)
├── scripts/                       # Build and deployment scripts
├── tests/                         # Integration tests only
└── tools/                         # Development tools and monitoring
```

## 🔄 Migration Steps

### Phase 1: Create New Structure
1. Create `apps/` directory
2. Move `mobile_app/` → `apps/mobile/`
3. Reorganize `backend/` → `apps/backend/src/`
4. Rename `ml_models/` → `apps/ml-service/`

### Phase 2: Consolidate Configuration
1. Move all config files to `config/`
2. Eliminate duplicate `.env` files
3. Centralize Docker configurations

### Phase 3: Fix Backend Models
1. Consolidate scattered model files
2. Fix foreign key type mismatches
3. Update import statements

### Phase 4: Clean Dependencies
1. Keep only one requirements file per service
2. Remove duplicate dependency definitions
3. Update Docker build contexts

### Phase 5: Update Build Systems
1. Update docker-compose.yml paths
2. Fix Dockerfile contexts
3. Update CI/CD scripts

## 🎯 Benefits After Restructuring

1. **Clear Separation of Concerns**: Each app in its own directory
2. **Consistent Organization**: Similar structure across all components
3. **Easier Navigation**: Logical grouping of related files
4. **Better Scalability**: Easy to add new services or components
5. **Improved Developer Experience**: Clear where to find and add code
6. **Simplified Build Process**: Clear dependency management
7. **Better Documentation**: Centralized and organized docs

## ⚠️ Risks and Mitigation

### Risks:
- Breaking existing imports
- Docker build context issues
- IDE configuration problems

### Mitigation:
- Update all import statements systematically
- Test each component after moving
- Update IDE project files
- Maintain backward compatibility during transition
