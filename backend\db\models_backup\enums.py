"""Enums for database models."""

from enum import Enum, auto

class UserRole(str, Enum):
    """User role enum."""
    USER = "user"
    ADMIN = "admin"

class DeviceType(str, Enum):
    """Device type enum."""
    MOBILE = "mobile"
    DESKTOP = "desktop"
    TABLET = "tablet"
    OTHER = "other"

class Platform(str, Enum):
    """Device platform enum."""
    IOS = "ios"
    ANDROID = "android"
    WINDOWS = "windows"
    MACOS = "macos"
    LINUX = "linux"
    OTHER = "other"

class EventType(str, Enum):
    """Behavioral event type enum."""
    TOUCH = "touch"
    MOTION = "motion"
    TYPING = "typing"
    GAIT = "gait"
    LOCATION = "location"
    OTHER = "other"
