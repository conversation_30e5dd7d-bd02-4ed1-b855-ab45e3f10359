import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mockito/mockito.dart';
import 'package:trustchain_auth/features/banking/presentation/widgets/account_summary_card.dart';
import 'package:trustchain_auth/features/banking/presentation/widgets/transactions_list.dart';
import 'package:trustchain_auth/features/banking/presentation/widgets/transfer_dialog.dart';
import 'package:trustchain_auth/features/banking/domain/models/models.dart';
import 'package:trustchain_auth/features/banking/presentation/bloc/banking_bloc.dart';

class MockBankingBloc extends Mock implements BankingBloc {}

void main() {
  late MockBankingBloc mockBloc;

  setUp(() {
    mockBloc = MockBankingBloc();
  });

  group('AccountSummaryCard Widget Tests', () {
    testWidgets('displays account summary correctly', (WidgetTester tester) async {
      final summary = AccountSummary(
        totalBalance: 1000.0,
        balanceByCurrency: {'USD': 1000.0},
        totalAccounts: 2,
        recentTransactions: [],
        monthlyTransactionTotals: {'January': 500.0},
        totalIncome: 1500.0,
        totalExpenses: 500.0,
        lastUpdated: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AccountSummaryCard(summary: summary),
          ),
        ),
      );

      expect(find.text('\$1,000.00'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
    });

    testWidgets('handles zero balance gracefully', (WidgetTester tester) async {
      final summary = AccountSummary(
        totalBalance: 0.0,
        balanceByCurrency: {'USD': 0.0},
        totalAccounts: 1,
        recentTransactions: [],
        monthlyTransactionTotals: {},
        totalIncome: 0.0,
        totalExpenses: 0.0,
        lastUpdated: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AccountSummaryCard(summary: summary),
          ),
        ),
      );

      expect(find.text('\$0.00'), findsOneWidget);
    });
  });

  group('TransactionsList Widget Tests', () {
    final transactions = [
      Transaction(
        id: '1',
        accountId: 'account1',
        userId: 'user1',
        amount: 100.0,
        currency: 'USD',
        type: TransactionType.credit,
        status: TransactionStatus.completed,
        description: 'Deposit',
        timestamp: DateTime.now(),
      ),
      Transaction(
        id: '2',
        accountId: 'account1',
        userId: 'user1',
        amount: -50.0,
        currency: 'USD',
        type: TransactionType.debit,
        status: TransactionStatus.completed,
        description: 'Withdrawal',
        timestamp: DateTime.now(),
      ),
    ];

    testWidgets('displays transactions correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionsList(transactions: transactions),
          ),
        ),
      );

      expect(find.text('Deposit'), findsOneWidget);
      expect(find.text('Withdrawal'), findsOneWidget);
      expect(find.text('\$100.00'), findsOneWidget);
      expect(find.text('-\$50.00'), findsOneWidget);
    });

    testWidgets('shows empty state when no transactions',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionsList(transactions: []),
          ),
        ),
      );

      expect(find.text('No transactions yet'), findsOneWidget);
    });
  });

  group('TransferDialog Widget Tests', () {
    testWidgets('validates input fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () => showDialog(
                context: context,
                builder: (context) => const TransferDialog(),
              ),
              child: Text('Show Dialog'),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Try to submit without input
      await tester.tap(find.text('Transfer'));
      await tester.pump();

      expect(find.text('Amount is required'), findsOneWidget);
      expect(find.text('Recipient is required'), findsOneWidget);

      // Fill invalid amount
      await tester.enterText(find.byType(TextField).first, '-100');
      await tester.pump();

      expect(find.text('Amount must be positive'), findsOneWidget);
    });
  });
}
