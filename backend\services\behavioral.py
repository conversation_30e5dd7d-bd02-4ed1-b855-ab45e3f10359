"""Behavioral data collection and risk assessment service."""

from typing import Dict, List, Optional, Tuple, Any
import httpx
from datetime import datetime, timedelta
import json
import logging

from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from core.config import settings
from db.models import (
    <PERSON>r,
    <PERSON><PERSON>,
    BehavioralEvent,
    BehavioralFeature,
    BehavioralProfile,
    RiskAssessment
)
from schemas.behavioral import (
    BehavioralEventCreate,
    BehavioralFeatureCreate,
    RiskAssessmentCreate,
    RiskAssessmentResponse
)

logger = logging.getLogger(__name__)

class BehavioralService:
    """Service for behavioral data collection and risk assessment."""
    
    def __init__(self, session: AsyncSession):
        """Initialize service.
        
        Args:
            session: Database session
        """
        self.session = session
        self.ml_service_url = settings.MODEL_SERVER_URL
        self.risk_threshold = settings.RISK_THRESHOLD
        self.confidence_threshold = settings.CONFIDENCE_THRESHOLD
    
    async def collect_and_analyze(
        self,
        user: User,
        device: Device,
        typing_data: Optional[List[Dict]] = None,
        touch_data: Optional[List[Dict]] = None,
        session_id: Optional[str] = None
    ) -> RiskAssessmentResponse:
        """Collect behavioral data and get risk assessment.
        
        Args:
            user: User model
            device: Device model
            typing_data: Optional typing event data
            touch_data: Optional touch event data
            session_id: Optional session ID
            
        Returns:
            Risk assessment results
        """
        try:
            # Create behavioral event
            if typing_data:
                await self.create_event(
                    BehavioralEventCreate(
                        device_id=str(device.id),
                        session_id=session_id,
                        event_type="typing",
                        data=typing_data,
                        context={"device_info": device.device_info}
                    ),
                    str(user.id)
                )
            
            if touch_data:
                await self.create_event(
                    BehavioralEventCreate(
                        device_id=str(device.id),
                        session_id=session_id,
                        event_type="touch",
                        data=touch_data,
                        context={"device_info": device.device_info}
                    ),
                    str(user.id)
                )
            
            # Get risk assessment from ML service
            data = {
                "user_id": str(user.id),
                "typing_data": typing_data,
                "touch_data": touch_data,
                "device_info": device.device_info
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.ml_service_url}/predict/risk",
                    json=data,
                    timeout=10.0
                )
                response.raise_for_status()
                assessment = response.json()
            
            # Create risk assessment
            risk_assessment = await self.create_risk_assessment(
                RiskAssessmentCreate(
                    device_id=str(device.id),
                    session_id=session_id,
                    risk_score=assessment["risk_score"],
                    anomaly_score=assessment["anomaly_score"],
                    confidence=assessment["confidence"],
                    features_used=assessment["features_used"],
                    model_version=assessment["model_version"],
                    decision="allow" if assessment["risk_score"] < self.risk_threshold else "challenge",
                    explanation=assessment.get("explanation"),
                    metadata={"device_info": device.device_info}
                ),
                str(user.id)
            )
            
            # Create auth event
            auth_event = AuthEvent(
                user_id=user.id,
                device_id=device.id,
                event_type="behavioral_assessment",
                event_data={
                    "risk_score": risk_assessment.risk_score,
                    "confidence": risk_assessment.confidence,
                    "decision": risk_assessment.decision
                }
            )
            self.session.add(auth_event)
            await self.session.commit()
            
            return RiskAssessmentResponse(
                risk_score=risk_assessment.risk_score,
                anomaly_score=risk_assessment.anomaly_score,
                confidence=risk_assessment.confidence,
                decision=risk_assessment.decision,
                features_used=risk_assessment.features_used,
                explanation=risk_assessment.explanation
            )
            
        except httpx.HTTPError as e:
            logger.error(f"ML service request failed: {e}")
            raise
        except Exception as e:
            logger.error(f"Error in behavioral analysis: {e}")
            raise
    
    async def create_event(
        self, event: BehavioralEventCreate, user_id: str
    ) -> BehavioralEvent:
        """Create a new behavioral event.
        
        Args:
            event: Event data
            user_id: User ID
            
        Returns:
            Created event
        """
        db_event = BehavioralEvent(
            user_id=user_id,
            device_id=event.device_id,
            session_id=event.session_id,
            event_type=event.event_type,
            data=event.data,
            context=event.context
        )
        
        self.session.add(db_event)
        await self.session.commit()
        await self.session.refresh(db_event)
        
        return db_event
    
    async def create_feature(
        self, feature: BehavioralFeatureCreate, user_id: str
    ) -> BehavioralFeature:
        """Create a new behavioral feature.
        
        Args:
            feature: Feature data
            user_id: User ID
            
        Returns:
            Created feature
        """
        db_feature = BehavioralFeature(
            user_id=user_id,
            device_id=feature.device_id,
            session_id=feature.session_id,
            feature_type=feature.feature_type,
            features=feature.features,
            metadata=feature.metadata
        )
        
        self.session.add(db_feature)
        await self.session.commit()
        await self.session.refresh(db_feature)
        
        return db_feature
    
    async def create_risk_assessment(
        self, assessment: RiskAssessmentCreate, user_id: str
    ) -> RiskAssessment:
        """Create a new risk assessment.
        
        Args:
            assessment: Assessment data
            user_id: User ID
            
        Returns:
            Created assessment
        """
        db_assessment = RiskAssessment(
            user_id=user_id,
            device_id=assessment.device_id,
            session_id=assessment.session_id,
            risk_score=assessment.risk_score,
            anomaly_score=assessment.anomaly_score,
            confidence=assessment.confidence,
            features_used=assessment.features_used,
            model_version=assessment.model_version,
            decision=assessment.decision,
            explanation=assessment.explanation,
            metadata=assessment.metadata
        )
        
        self.session.add(db_assessment)
        await self.session.commit()
        await self.session.refresh(db_assessment)
        
        return db_assessment
    
    async def get_user_profile(
        self, user_id: str, active_only: bool = True
    ) -> Optional[BehavioralProfile]:
        """Get user's behavioral profile.
        
        Args:
            user_id: User ID
            active_only: Whether to return only active profile
            
        Returns:
            User's behavioral profile
        """
        query = select(BehavioralProfile).where(
            BehavioralProfile.user_id == user_id
        )
        
        if active_only:
            query = query.where(BehavioralProfile.is_active == True)
        
        result = await self.session.execute(query)
        return result.scalar_one_or_none()
    
    async def update_user_profile(
        self, user_id: str,
        latent_features: Dict,
        metrics: Dict,
        thresholds: Dict,
        metadata: Dict
    ) -> BehavioralProfile:
        """Update user's behavioral profile.
        
        Args:
            user_id: User ID
            latent_features: Updated latent features
            metrics: Profile quality metrics
            thresholds: Anomaly detection thresholds
            metadata: Profile metadata
            
        Returns:
            Updated profile
        """
        profile = await self.get_user_profile(user_id)
        
        if profile:
            # Update existing profile
            profile.latent_features = latent_features
            profile.metrics = metrics
            profile.thresholds = thresholds
            profile.metadata = metadata
            profile.updated_at = datetime.utcnow()
        else:
            # Create new profile
            profile = BehavioralProfile(
                user_id=user_id,
                profile_type='baseline',
                latent_features=latent_features,
                metrics=metrics,
                thresholds=thresholds,
                metadata=metadata
            )
            self.session.add(profile)
        
        await self.session.commit()
        await self.session.refresh(profile)
        
        return profile
    
    async def get_user_events(
        self,
        user_id: str,
        event_type: Optional[str] = None,
        device_id: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 100
    ) -> List[BehavioralEvent]:
        """Get user's behavioral events.
        
        Args:
            user_id: User ID
            event_type: Optional event type filter
            device_id: Optional device ID filter
            session_id: Optional session ID filter
            limit: Maximum number of events to return
            
        Returns:
            List of events
        """
        query = select(BehavioralEvent).where(
            BehavioralEvent.user_id == user_id
        )
        
        if event_type:
            query = query.where(BehavioralEvent.event_type == event_type)
        if device_id:
            query = query.where(BehavioralEvent.device_id == device_id)
        if session_id:
            query = query.where(BehavioralEvent.session_id == session_id)
            
        query = query.order_by(BehavioralEvent.timestamp.desc()).limit(limit)
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def get_recent_risk_assessments(
        self,
        user_id: str,
        device_id: Optional[str] = None,
        session_id: Optional[str] = None,
        limit: int = 10
    ) -> List[RiskAssessment]:
        """Get user's recent risk assessments.
        
        Args:
            user_id: User ID
            device_id: Optional device ID filter
            session_id: Optional session ID filter
            limit: Maximum number of assessments to return
            
        Returns:
            List of risk assessments
        """
        query = select(RiskAssessment).where(
            RiskAssessment.user_id == user_id
        )
        
        if device_id:
            query = query.where(RiskAssessment.device_id == device_id)
        if session_id:
            query = query.where(RiskAssessment.session_id == session_id)
            
        query = query.order_by(RiskAssessment.timestamp.desc()).limit(limit)
        
        result = await self.session.execute(query)
        return result.scalars().all()
    
    async def check_risk_threshold(
        self,
        user_id: str,
        device_id: str,
        session_id: str,
        window_minutes: int = 30
    ) -> Tuple[bool, float, float]:
        """Check if risk exceeds thresholds.
        
        Args:
            user_id: User ID
            device_id: Device ID
            session_id: Session ID
            window_minutes: Time window to check in minutes
            
        Returns:
            Tuple of (is_risky, avg_risk_score, avg_confidence)
        """
        # Get recent assessments within time window
        since = datetime.utcnow() - timedelta(minutes=window_minutes)
        
        query = select(RiskAssessment).where(and_(
            RiskAssessment.user_id == user_id,
            RiskAssessment.device_id == device_id,
            RiskAssessment.session_id == session_id,
            RiskAssessment.timestamp >= since
        ))
        
        result = await self.session.execute(query)
        assessments = result.scalars().all()
        
        if not assessments:
            return False, 0.0, 0.0
        
        # Calculate averages
        avg_risk = sum(a.risk_score for a in assessments) / len(assessments)
        avg_conf = sum(a.confidence for a in assessments) / len(assessments)
        
        # Check if risk exceeds threshold with sufficient confidence
        is_risky = (
            avg_risk >= self.risk_threshold and
            avg_conf >= self.confidence_threshold
        )
        
        return is_risky, avg_risk, avg_conf
