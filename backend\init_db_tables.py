#!/usr/bin/env python3
"""Initialize database tables."""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from db.models import Base
from core.config import settings

async def init_db():
    """Create all database tables."""
    print(f"Connecting to database: {settings.SQLALCHEMY_DATABASE_URI}")
    
    engine = create_async_engine(settings.SQLALCHEMY_DATABASE_URI)
    
    async with engine.begin() as conn:
        print("Creating all tables...")
        await conn.run_sync(Base.metadata.create_all)
        print("Tables created successfully!")
    
    await engine.dispose()

if __name__ == "__main__":
    asyncio.run(init_db())
